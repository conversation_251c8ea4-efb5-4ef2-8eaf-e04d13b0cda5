const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Import routes and error handling
const excelRoutes = require('./routes/excelRoutes');
const { errorMiddleware } = require('./utils/errorHandler');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, 'output');
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Routes
app.get('/', (_req, res) => {
    res.json({
        message: 'Excel Worksheet Cloning API',
        endpoints: {
            'GET /': 'This help message',
            'GET /health': 'Health check',
            'GET /api/excel/worksheet-info': 'Get worksheet information from existing Excel file',
            'POST /api/excel/clone-worksheet': 'Clone worksheet from existing Excel file',
            'POST /api/excel/upload-and-clone': 'Upload Excel file and clone worksheet'
        }
    });
});

// Health check endpoint
app.get('/health', (_req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Use Excel routes
app.use('/api/excel', excelRoutes);

// Error handling middleware
app.use(errorMiddleware);

// 404 handler
app.use((_req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: 'The requested endpoint does not exist'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Visit http://localhost:${PORT} for API documentation`);
});

module.exports = app;
