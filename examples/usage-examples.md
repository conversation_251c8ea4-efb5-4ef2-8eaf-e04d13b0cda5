# Usage Examples

This document provides practical examples of how to use the Excel Worksheet Cloning API.

## Prerequisites

1. Start the server:
```bash
npm start
```

2. The server will be available at `http://localhost:3000`

## Example 1: Basic Worksheet Cloning

Clone the existing worksheet 3 times (default):

```bash
curl -X POST http://localhost:3000/api/excel/clone-worksheet \
  -H "Content-Type: application/json" \
  -d '{}'
```

## Example 2: Custom Number of Copies

Clone the worksheet 5 times:

```bash
curl -X POST http://localhost:3000/api/excel/clone-worksheet \
  -H "Content-Type: application/json" \
  -d '{"numberOfCopies": 5}'
```

## Example 3: Clone Specific Worksheet

Clone a specific worksheet by name:

```bash
curl -X POST http://localhost:3000/api/excel/clone-worksheet \
  -H "Content-Type: application/json" \
  -d '{"worksheetName": "Phiếu xuất kho", "numberOfCopies": 2}'
```

## Example 4: Get Worksheet Information

Before cloning, you can check what worksheets are available:

```bash
curl http://localhost:3000/api/excel/worksheet-info
```

Response:
```json
{
  "success": true,
  "file": "warehouse_explanation.xlsx",
  "worksheetCount": 1,
  "worksheets": [
    {
      "id": 1,
      "name": "Phiếu xuất kho",
      "rowCount": 7,
      "columnCount": 7,
      "actualRowCount": 7,
      "actualColumnCount": 7,
      "hasImages": true,
      "hasMergedCells": true
    }
  ]
}
```

## Example 5: Upload and Clone

Upload your own Excel file and clone its worksheet:

```bash
curl -X POST http://localhost:3000/api/excel/upload-and-clone \
  -F "excelFile=@/path/to/your/file.xlsx" \
  -F "numberOfCopies=3"
```

## Example 6: JavaScript/Node.js Integration

```javascript
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function cloneWorksheet() {
  try {
    const response = await axios.post('http://localhost:3000/api/excel/clone-worksheet', {
      numberOfCopies: 3
    });
    
    console.log('Cloning successful:', response.data);
    console.log('Output file:', response.data.outputFile);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

async function uploadAndClone(filePath) {
  try {
    const form = new FormData();
    form.append('excelFile', fs.createReadStream(filePath));
    form.append('numberOfCopies', '2');
    
    const response = await axios.post('http://localhost:3000/api/excel/upload-and-clone', form, {
      headers: form.getHeaders()
    });
    
    console.log('Upload and clone successful:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Usage
cloneWorksheet();
// uploadAndClone('./my-excel-file.xlsx');
```

## Example 7: Python Integration

```python
import requests
import json

def clone_worksheet(num_copies=3):
    url = 'http://localhost:3000/api/excel/clone-worksheet'
    data = {'numberOfCopies': num_copies}
    
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"Cloning successful: {result['message']}")
        print(f"Output file: {result['outputFile']}")
        return result
    else:
        print(f"Error: {response.json()}")
        return None

def upload_and_clone(file_path, num_copies=3):
    url = 'http://localhost:3000/api/excel/upload-and-clone'
    
    with open(file_path, 'rb') as f:
        files = {'excelFile': f}
        data = {'numberOfCopies': num_copies}
        
        response = requests.post(url, files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"Upload and clone successful: {result['message']}")
            return result
        else:
            print(f"Error: {response.json()}")
            return None

# Usage
clone_worksheet(5)
# upload_and_clone('./my-excel-file.xlsx', 2)
```

## Example 8: Error Handling

The API provides detailed error information:

```bash
# Try to clone a non-existent worksheet
curl -X POST http://localhost:3000/api/excel/clone-worksheet \
  -H "Content-Type: application/json" \
  -d '{"worksheetName": "NonExistent"}'
```

Response:
```json
{
  "statusCode": 404,
  "error": "Worksheet Not Found",
  "message": "Worksheet 'NonExistent' not found",
  "code": "WORKSHEET_NOT_FOUND"
}
```

## Example 9: Validation Errors

```bash
# Try with invalid number of copies
curl -X POST http://localhost:3000/api/excel/clone-worksheet \
  -H "Content-Type: application/json" \
  -d '{"numberOfCopies": 15}'
```

Response:
```json
{
  "statusCode": 400,
  "error": "Validation Error",
  "message": "Validation failed: numberOfCopies: Number of copies cannot exceed 10 (to prevent excessive file size)",
  "code": "VALIDATION_ERROR"
}
```

## Testing

Run the automated test suite:

```bash
npm test
```

This will test all endpoints and functionality automatically.

## Output Files

All generated Excel files are saved in the `output/` directory with timestamps:
- `cloned_warehouse_explanation_2025-07-11T03-21-34.xlsx`

The files contain:
- Original worksheet with all formatting, images, and merged cells
- Cloned worksheets with identical content and formatting
- All complex Excel features preserved

## Features Demonstrated

✅ **Image Preservation**: Embedded images are copied to all cloned worksheets
✅ **Merged Cells**: Cell ranges like A1:G7 are properly merged in copies  
✅ **Formatting**: All cell styling, fonts, colors, borders are preserved
✅ **Data Integrity**: All cell values, formulas, and data validation rules maintained
✅ **Error Handling**: Comprehensive error messages for various failure scenarios
✅ **Validation**: Input validation prevents invalid operations
