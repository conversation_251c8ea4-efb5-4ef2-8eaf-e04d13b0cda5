const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

class ExcelService {
    constructor() {
        this.workbook = null;
    }

    /**
     * Read an Excel file and load it into memory
     * @param {string} filePath - Path to the Excel file
     * @returns {Promise<ExcelJS.Workbook>} - The loaded workbook
     */
    async readExcelFile(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }

            this.workbook = new ExcelJS.Workbook();
            await this.workbook.xlsx.readFile(filePath);
            
            console.log(`Successfully loaded Excel file: ${filePath}`);
            console.log(`Number of worksheets: ${this.workbook.worksheets.length}`);
            
            return this.workbook;
        } catch (error) {
            console.error('Error reading Excel file:', error);
            throw new Error(`Failed to read Excel file: ${error.message}`);
        }
    }

    /**
     * Get information about all worksheets in the workbook
     * @returns {Array} - Array of worksheet information
     */
    getWorksheetInfo() {
        if (!this.workbook) {
            throw new Error('No workbook loaded. Please read an Excel file first.');
        }

        return this.workbook.worksheets.map(worksheet => ({
            id: worksheet.id,
            name: worksheet.name,
            rowCount: worksheet.rowCount,
            columnCount: worksheet.columnCount,
            actualRowCount: worksheet.actualRowCount,
            actualColumnCount: worksheet.actualColumnCount,
            hasImages: this.hasImages(worksheet),
            hasMergedCells: this.hasMergedCells(worksheet)
        }));
    }

    /**
     * Check if a worksheet has images
     * @param {ExcelJS.Worksheet} worksheet - The worksheet to check
     * @returns {boolean} - True if worksheet has images
     */
    hasImages(worksheet) {
        try {
            return worksheet.getImages && worksheet.getImages().length > 0;
        } catch (error) {
            // Some versions of ExcelJS might not have getImages method
            return false;
        }
    }

    /**
     * Check if a worksheet has merged cells
     * @param {ExcelJS.Worksheet} worksheet - The worksheet to check
     * @returns {boolean} - True if worksheet has merged cells
     */
    hasMergedCells(worksheet) {
        try {
            // Check if there are any merged cell ranges
            const mergedCells = [];
            worksheet.eachRow((row, rowNumber) => {
                row.eachCell((cell, colNumber) => {
                    if (cell.master && cell.master !== cell) {
                        mergedCells.push(cell);
                    }
                });
            });
            return mergedCells.length > 0;
        } catch (error) {
            console.warn('Error checking merged cells:', error);
            return false;
        }
    }

    /**
     * Get the first worksheet from the workbook
     * @returns {ExcelJS.Worksheet} - The first worksheet
     */
    getFirstWorksheet() {
        if (!this.workbook || this.workbook.worksheets.length === 0) {
            throw new Error('No worksheets found in the workbook');
        }
        return this.workbook.worksheets[0];
    }

    /**
     * Get a worksheet by name
     * @param {string} name - Name of the worksheet
     * @returns {ExcelJS.Worksheet} - The worksheet
     */
    getWorksheetByName(name) {
        if (!this.workbook) {
            throw new Error('No workbook loaded');
        }
        
        const worksheet = this.workbook.getWorksheet(name);
        if (!worksheet) {
            throw new Error(`Worksheet '${name}' not found`);
        }
        
        return worksheet;
    }

    /**
     * Save the workbook to a file
     * @param {string} outputPath - Path where to save the file
     * @returns {Promise<void>}
     */
    async saveWorkbook(outputPath) {
        if (!this.workbook) {
            throw new Error('No workbook to save');
        }

        try {
            // Ensure output directory exists
            const outputDir = path.dirname(outputPath);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            await this.workbook.xlsx.writeFile(outputPath);
            console.log(`Workbook saved to: ${outputPath}`);
        } catch (error) {
            console.error('Error saving workbook:', error);
            throw new Error(`Failed to save workbook: ${error.message}`);
        }
    }

    /**
     * Create a new workbook instance
     * @returns {ExcelJS.Workbook} - New workbook instance
     */
    createNewWorkbook() {
        this.workbook = new ExcelJS.Workbook();
        return this.workbook;
    }
}

module.exports = ExcelService;
