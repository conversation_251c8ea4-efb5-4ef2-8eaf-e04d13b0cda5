# Excel Worksheet Cloning API

A Node.js Express application that integrates with ExcelJS library to clone Excel worksheets while preserving all formatting, images, and merged cells.

## Features

- ✅ **Complete Worksheet Cloning**: Clone worksheets with all content and formatting preserved
- ✅ **Image Preservation**: Maintains embedded images within cells during cloning
- ✅ **Merged Cell Support**: Preserves merged cell ranges and their formatting
- ✅ **Formula & Data Validation**: Maintains formulas, data validation rules, and hyperlinks
- ✅ **Comprehensive Error Handling**: Robust error handling for Excel operations
- ✅ **RESTful API**: Clean REST API endpoints for easy integration
- ✅ **File Upload Support**: Upload Excel files or work with existing files
- ✅ **Validation**: Input validation and sanitization

## Technical Highlights

This solution addresses the main technical challenges of Excel worksheet cloning:

1. **Image Handling**: Special logic to copy embedded images without losing references
2. **Merged Cells**: Proper handling of merged cell ranges during duplication
3. **Circular Reference Prevention**: Avoids JSON serialization issues with ExcelJS objects
4. **Memory Management**: Efficient handling of large Excel files
5. **Error Recovery**: Comprehensive error handling for various Excel file issues

## Installation

1. Clone or download this project
2. Install dependencies:
```bash
npm install
```

3. Start the server:
```bash
npm start
# or for development with auto-reload:
npm run dev
```

The server will start on port 3000 by default.

## API Endpoints

### GET /
Returns API documentation and available endpoints.

### GET /health
Health check endpoint.

### GET /api/excel/worksheet-info
Get information about worksheets in the existing Excel file (`warehouse_explanation.xlsx`).

**Response:**
```json
{
  "success": true,
  "file": "warehouse_explanation.xlsx",
  "worksheetCount": 1,
  "worksheets": [
    {
      "id": 1,
      "name": "Phiếu xuất kho",
      "rowCount": 7,
      "columnCount": 7,
      "actualRowCount": 7,
      "actualColumnCount": 7,
      "hasImages": true,
      "hasMergedCells": true
    }
  ]
}
```

### POST /api/excel/clone-worksheet
Clone worksheet from the existing Excel file in the workspace.

**Request Body:**
```json
{
  "worksheetName": "Sheet1",  // Optional: worksheet name to clone (uses first sheet if not specified)
  "numberOfCopies": 3         // Optional: number of copies to create (default: 3, max: 10)
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully cloned worksheet 'Phiếu xuất kho' 3 times",
  "sourceWorksheet": {
    "name": "Phiếu xuất kho",
    "details": {
      "name": "Phiếu xuất kho",
      "rowCount": 7,
      "columnCount": 7,
      "actualRowCount": 7,
      "actualColumnCount": 7,
      "mergedCells": ["A1:G7"],
      "imageCount": 1,
      "hasFormulas": false,
      "hasDataValidation": false,
      "hasHyperlinks": false
    }
  },
  "clonedWorksheets": [
    "Phiếu xuất kho_Copy_1",
    "Phiếu xuất kho_Copy_2",
    "Phiếu xuất kho_Copy_3"
  ],
  "outputFile": "cloned_warehouse_explanation_2025-07-11T03-21-34.xlsx",
  "outputPath": "/path/to/output/cloned_warehouse_explanation_2025-07-11T03-21-34.xlsx",
  "totalWorksheets": 4,
  "worksheetInfo": [...]
}
```

### POST /api/excel/upload-and-clone
Upload an Excel file and clone its worksheet.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- File field: `excelFile`
- Additional fields: `worksheetName` (optional), `numberOfCopies` (optional)

**Example using curl:**
```bash
curl -X POST http://localhost:3000/api/excel/upload-and-clone \
  -F "excelFile=@your-file.xlsx" \
  -F "numberOfCopies=3"
```

## Usage Examples

### Using curl

1. **Get worksheet information:**
```bash
curl http://localhost:3000/api/excel/worksheet-info
```

2. **Clone worksheet (default 3 copies):**
```bash
curl -X POST http://localhost:3000/api/excel/clone-worksheet \
  -H "Content-Type: application/json" \
  -d '{"numberOfCopies": 3}'
```

3. **Clone specific worksheet:**
```bash
curl -X POST http://localhost:3000/api/excel/clone-worksheet \
  -H "Content-Type: application/json" \
  -d '{"worksheetName": "Sheet1", "numberOfCopies": 5}'
```

4. **Upload and clone:**
```bash
curl -X POST http://localhost:3000/api/excel/upload-and-clone \
  -F "excelFile=@myfile.xlsx" \
  -F "numberOfCopies=2"
```

## Project Structure

```
├── index.js                 # Main Express server
├── package.json             # Dependencies and scripts
├── services/
│   ├── excelService.js      # Excel file reading and basic operations
│   └── worksheetCloner.js   # Core worksheet cloning logic
├── routes/
│   └── excelRoutes.js       # API route handlers
├── utils/
│   ├── validation.js        # Input validation utilities
│   └── errorHandler.js      # Error handling utilities
├── uploads/                 # Temporary upload directory
├── output/                  # Generated Excel files
└── warehouse_explanation.xlsx # Sample Excel file
```

## Dependencies

- **express**: Web framework
- **exceljs**: Excel file manipulation library
- **multer**: File upload handling
- **cors**: Cross-origin resource sharing

## Error Handling

The application includes comprehensive error handling for:

- Invalid Excel files
- Missing worksheets
- File permission issues
- Memory limitations
- Circular reference issues
- Validation errors

## Limitations

- Maximum file size: 50MB
- Maximum number of copies: 10 (configurable)
- Supported formats: .xlsx, .xls, .xlsm

## Development

For development with auto-reload:
```bash
npm run dev
```

## Testing

The application has been tested with:
- ✅ Excel files containing images
- ✅ Excel files with merged cells
- ✅ Complex formatting and styling
- ✅ Multiple worksheet scenarios
- ✅ Error conditions and edge cases

## License

ISC License
