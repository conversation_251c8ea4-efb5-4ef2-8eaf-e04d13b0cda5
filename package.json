{"name": "exceljstest", "version": "1.0.0", "description": "Node.js Express application for Excel worksheet cloning with ExcelJS", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node test-api.js", "test:manual": "echo \"Manual testing: Start server with 'npm start' then run 'npm test' in another terminal\""}, "keywords": ["excel", "exceljs", "express", "worksheet", "clone"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "exceljs": "^4.4.0", "express": "^5.1.0", "multer": "^2.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}