#!/usr/bin/env node

/**
 * Simple test script to validate the Excel Worksheet Cloning API
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({ statusCode: res.statusCode, body: jsonBody });
                } catch (error) {
                    resolve({ statusCode: res.statusCode, body: body });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

// Test functions
async function testHealthCheck() {
    console.log('\n🔍 Testing Health Check...');
    try {
        const response = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/health',
            method: 'GET'
        });

        if (response.statusCode === 200 && response.body.status === 'OK') {
            console.log('✅ Health check passed');
            return true;
        } else {
            console.log('❌ Health check failed:', response);
            return false;
        }
    } catch (error) {
        console.log('❌ Health check error:', error.message);
        return false;
    }
}

async function testWorksheetInfo() {
    console.log('\n🔍 Testing Worksheet Info...');
    try {
        const response = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/excel/worksheet-info',
            method: 'GET'
        });

        if (response.statusCode === 200 && response.body.success) {
            console.log('✅ Worksheet info retrieved successfully');
            console.log(`   📊 Found ${response.body.worksheetCount} worksheet(s)`);
            response.body.worksheets.forEach(ws => {
                console.log(`   📋 "${ws.name}": ${ws.rowCount}x${ws.columnCount}, Images: ${ws.hasImages}, Merged: ${ws.hasMergedCells}`);
            });
            return response.body.worksheets[0]; // Return first worksheet for next test
        } else {
            console.log('❌ Worksheet info failed:', response);
            return null;
        }
    } catch (error) {
        console.log('❌ Worksheet info error:', error.message);
        return null;
    }
}

async function testWorksheetCloning(worksheetName = null) {
    console.log('\n🔍 Testing Worksheet Cloning...');
    try {
        const requestData = JSON.stringify({
            worksheetName: worksheetName,
            numberOfCopies: 2
        });

        const response = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/excel/clone-worksheet',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(requestData)
            }
        }, requestData);

        if (response.statusCode === 200 && response.body.success) {
            console.log('✅ Worksheet cloning successful');
            console.log(`   📋 Source: "${response.body.sourceWorksheet.name}"`);
            console.log(`   📄 Created ${response.body.clonedWorksheets.length} copies:`);
            response.body.clonedWorksheets.forEach(name => {
                console.log(`      - ${name}`);
            });
            console.log(`   💾 Output file: ${response.body.outputFile}`);
            
            // Check if output file exists
            if (fs.existsSync(response.body.outputPath)) {
                console.log('✅ Output file created successfully');
                const stats = fs.statSync(response.body.outputPath);
                console.log(`   📏 File size: ${Math.round(stats.size / 1024)} KB`);
            } else {
                console.log('❌ Output file not found');
            }
            
            return true;
        } else {
            console.log('❌ Worksheet cloning failed:', response);
            return false;
        }
    } catch (error) {
        console.log('❌ Worksheet cloning error:', error.message);
        return false;
    }
}

async function testApiDocumentation() {
    console.log('\n🔍 Testing API Documentation...');
    try {
        const response = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/',
            method: 'GET'
        });

        if (response.statusCode === 200 && response.body.message) {
            console.log('✅ API documentation accessible');
            console.log(`   📖 Message: ${response.body.message}`);
            console.log(`   🔗 Available endpoints: ${Object.keys(response.body.endpoints).length}`);
            return true;
        } else {
            console.log('❌ API documentation failed:', response);
            return false;
        }
    } catch (error) {
        console.log('❌ API documentation error:', error.message);
        return false;
    }
}

async function testErrorHandling() {
    console.log('\n🔍 Testing Error Handling...');
    try {
        // Test invalid worksheet name
        const requestData = JSON.stringify({
            worksheetName: 'NonExistentWorksheet',
            numberOfCopies: 1
        });

        const response = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/excel/clone-worksheet',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(requestData)
            }
        }, requestData);

        if (response.statusCode >= 400 && response.body.error) {
            console.log('✅ Error handling working correctly');
            console.log(`   ⚠️  Error: ${response.body.message}`);
            return true;
        } else {
            console.log('❌ Error handling not working as expected:', response);
            return false;
        }
    } catch (error) {
        console.log('❌ Error handling test error:', error.message);
        return false;
    }
}

// Main test runner
async function runTests() {
    console.log('🚀 Starting Excel Worksheet Cloning API Tests');
    console.log('=' .repeat(50));

    const results = {
        total: 0,
        passed: 0,
        failed: 0
    };

    // Test 1: Health Check
    results.total++;
    if (await testHealthCheck()) {
        results.passed++;
    } else {
        results.failed++;
    }

    // Test 2: API Documentation
    results.total++;
    if (await testApiDocumentation()) {
        results.passed++;
    } else {
        results.failed++;
    }

    // Test 3: Worksheet Info
    results.total++;
    const worksheetInfo = await testWorksheetInfo();
    if (worksheetInfo) {
        results.passed++;
    } else {
        results.failed++;
    }

    // Test 4: Worksheet Cloning
    results.total++;
    if (await testWorksheetCloning(worksheetInfo?.name)) {
        results.passed++;
    } else {
        results.failed++;
    }

    // Test 5: Error Handling
    results.total++;
    if (await testErrorHandling()) {
        results.passed++;
    } else {
        results.failed++;
    }

    // Summary
    console.log('\n' + '=' .repeat(50));
    console.log('📊 Test Results Summary');
    console.log('=' .repeat(50));
    console.log(`Total Tests: ${results.total}`);
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`Success Rate: ${Math.round((results.passed / results.total) * 100)}%`);

    if (results.failed === 0) {
        console.log('\n🎉 All tests passed! The API is working correctly.');
    } else {
        console.log('\n⚠️  Some tests failed. Please check the server logs.');
    }

    console.log('\n💡 Tip: Check the output/ directory for generated Excel files.');
}

// Check if server is running before starting tests
async function checkServerStatus() {
    try {
        await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/health',
            method: 'GET'
        });
        return true;
    } catch (error) {
        return false;
    }
}

// Main execution
async function main() {
    console.log('Checking if server is running...');
    
    if (!(await checkServerStatus())) {
        console.log('❌ Server is not running on http://localhost:3000');
        console.log('Please start the server first with: npm start');
        process.exit(1);
    }

    console.log('✅ Server is running');
    await runTests();
}

// Run the tests
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { runTests, checkServerStatus };
