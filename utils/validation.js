/**
 * Validation utilities for Excel operations
 */

/**
 * Validate file extension for Excel files
 * @param {string} filename - The filename to validate
 * @returns {boolean} - True if valid Excel file extension
 */
function isValidExcelFile(filename) {
    if (!filename || typeof filename !== 'string') {
        return false;
    }
    
    const validExtensions = ['.xlsx', '.xls', '.xlsm'];
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return validExtensions.includes(extension);
}

/**
 * Validate MIME type for Excel files
 * @param {string} mimetype - The MIME type to validate
 * @returns {boolean} - True if valid Excel MIME type
 */
function isValidExcelMimeType(mimetype) {
    if (!mimetype || typeof mimetype !== 'string') {
        return false;
    }
    
    const validMimeTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'application/vnd.ms-excel.sheet.macroEnabled.12' // .xlsm
    ];
    
    return validMimeTypes.includes(mimetype);
}

/**
 * Validate worksheet name
 * @param {string} worksheetName - The worksheet name to validate
 * @returns {Object} - Validation result with isValid and error message
 */
function validateWorksheetName(worksheetName) {
    if (!worksheetName) {
        return { isValid: true, message: 'Worksheet name not provided, will use first worksheet' };
    }
    
    if (typeof worksheetName !== 'string') {
        return { isValid: false, message: 'Worksheet name must be a string' };
    }
    
    if (worksheetName.length === 0) {
        return { isValid: false, message: 'Worksheet name cannot be empty' };
    }
    
    if (worksheetName.length > 31) {
        return { isValid: false, message: 'Worksheet name cannot exceed 31 characters' };
    }
    
    // Excel worksheet name restrictions
    const invalidChars = ['\\', '/', '?', '*', '[', ']', ':'];
    for (const char of invalidChars) {
        if (worksheetName.includes(char)) {
            return { 
                isValid: false, 
                message: `Worksheet name cannot contain the character: ${char}` 
            };
        }
    }
    
    return { isValid: true, message: 'Valid worksheet name' };
}

/**
 * Validate number of copies
 * @param {any} numberOfCopies - The number of copies to validate
 * @returns {Object} - Validation result with isValid, value, and error message
 */
function validateNumberOfCopies(numberOfCopies) {
    // Default value
    if (numberOfCopies === undefined || numberOfCopies === null) {
        return { isValid: true, value: 3, message: 'Using default value of 3 copies' };
    }
    
    // Convert to number if it's a string
    let numCopies = numberOfCopies;
    if (typeof numberOfCopies === 'string') {
        numCopies = parseInt(numberOfCopies, 10);
        if (isNaN(numCopies)) {
            return { isValid: false, message: 'Number of copies must be a valid number' };
        }
    }
    
    if (typeof numCopies !== 'number') {
        return { isValid: false, message: 'Number of copies must be a number' };
    }
    
    if (!Number.isInteger(numCopies)) {
        return { isValid: false, message: 'Number of copies must be an integer' };
    }
    
    if (numCopies < 1) {
        return { isValid: false, message: 'Number of copies must be at least 1' };
    }
    
    if (numCopies > 10) {
        return { isValid: false, message: 'Number of copies cannot exceed 10 (to prevent excessive file size)' };
    }
    
    return { isValid: true, value: numCopies, message: 'Valid number of copies' };
}

/**
 * Validate file size
 * @param {number} fileSize - File size in bytes
 * @param {number} maxSize - Maximum allowed size in bytes (default: 50MB)
 * @returns {Object} - Validation result
 */
function validateFileSize(fileSize, maxSize = 50 * 1024 * 1024) {
    if (typeof fileSize !== 'number' || fileSize < 0) {
        return { isValid: false, message: 'Invalid file size' };
    }
    
    if (fileSize === 0) {
        return { isValid: false, message: 'File is empty' };
    }
    
    if (fileSize > maxSize) {
        const maxSizeMB = Math.round(maxSize / (1024 * 1024));
        const fileSizeMB = Math.round(fileSize / (1024 * 1024));
        return { 
            isValid: false, 
            message: `File size (${fileSizeMB}MB) exceeds maximum allowed size (${maxSizeMB}MB)` 
        };
    }
    
    return { isValid: true, message: 'Valid file size' };
}

/**
 * Sanitize filename for safe file operations
 * @param {string} filename - The filename to sanitize
 * @returns {string} - Sanitized filename
 */
function sanitizeFilename(filename) {
    if (!filename || typeof filename !== 'string') {
        return 'untitled';
    }
    
    // Remove or replace invalid characters
    return filename
        .replace(/[<>:"/\\|?*]/g, '_') // Replace invalid characters with underscore
        .replace(/\s+/g, '_') // Replace spaces with underscore
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
        .substring(0, 100); // Limit length
}

/**
 * Generate safe output filename
 * @param {string} originalName - Original filename
 * @param {string} prefix - Prefix for the filename
 * @returns {string} - Safe output filename
 */
function generateOutputFilename(originalName, prefix = 'cloned') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
    const baseName = originalName ? sanitizeFilename(originalName.replace(/\.[^/.]+$/, '')) : 'workbook';
    return `${prefix}_${baseName}_${timestamp}.xlsx`;
}

module.exports = {
    isValidExcelFile,
    isValidExcelMimeType,
    validateWorksheetName,
    validateNumberOfCopies,
    validateFileSize,
    sanitizeFilename,
    generateOutputFilename
};
