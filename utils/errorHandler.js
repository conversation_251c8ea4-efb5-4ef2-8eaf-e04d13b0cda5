/**
 * Error handling utilities for Excel operations
 */

class ExcelError extends <PERSON>rror {
    constructor(message, code = 'EXCEL_ERROR', statusCode = 500) {
        super(message);
        this.name = 'ExcelError';
        this.code = code;
        this.statusCode = statusCode;
    }
}

class ValidationError extends Error {
    constructor(message, field = null) {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
        this.statusCode = 400;
    }
}

class FileNotFoundError extends Error {
    constructor(message, filePath = null) {
        super(message);
        this.name = 'FileNotFoundError';
        this.filePath = filePath;
        this.statusCode = 404;
    }
}

/**
 * Handle Excel-related errors and convert them to appropriate HTTP responses
 * @param {Error} error - The error to handle
 * @returns {Object} - Error response object
 */
function handleExcelError(error) {
    console.error('Excel Error:', error);

    // Handle specific ExcelJS errors
    if (error.message.includes('Corrupt zip file')) {
        return {
            statusCode: 400,
            error: 'Invalid Excel File',
            message: 'The uploaded file appears to be corrupted or is not a valid Excel file',
            code: 'CORRUPT_FILE'
        };
    }

    if (error.message.includes('ENOENT') || error.message.includes('File not found')) {
        return {
            statusCode: 404,
            error: 'File Not Found',
            message: 'The specified Excel file could not be found',
            code: 'FILE_NOT_FOUND'
        };
    }

    if (error.message.includes('EACCES') || error.message.includes('permission denied')) {
        return {
            statusCode: 403,
            error: 'Permission Denied',
            message: 'Permission denied when accessing the Excel file',
            code: 'PERMISSION_DENIED'
        };
    }

    if (error.message.includes('EMFILE') || error.message.includes('too many open files')) {
        return {
            statusCode: 503,
            error: 'Service Temporarily Unavailable',
            message: 'Too many files are currently being processed. Please try again later.',
            code: 'TOO_MANY_FILES'
        };
    }

    if (error.message.includes('ENOSPC') || error.message.includes('no space left')) {
        return {
            statusCode: 507,
            error: 'Insufficient Storage',
            message: 'Not enough disk space to process the Excel file',
            code: 'INSUFFICIENT_STORAGE'
        };
    }

    // Handle worksheet-specific errors
    if (error.message.includes('Worksheet') && error.message.includes('not found')) {
        return {
            statusCode: 404,
            error: 'Worksheet Not Found',
            message: error.message,
            code: 'WORKSHEET_NOT_FOUND'
        };
    }

    // Handle memory-related errors
    if (error.message.includes('out of memory') || error.name === 'RangeError') {
        return {
            statusCode: 413,
            error: 'File Too Large',
            message: 'The Excel file is too large to process',
            code: 'FILE_TOO_LARGE'
        };
    }

    // Handle custom error types
    if (error instanceof ValidationError) {
        return {
            statusCode: error.statusCode,
            error: 'Validation Error',
            message: error.message,
            field: error.field,
            code: 'VALIDATION_ERROR'
        };
    }

    if (error instanceof FileNotFoundError) {
        return {
            statusCode: error.statusCode,
            error: 'File Not Found',
            message: error.message,
            filePath: error.filePath,
            code: 'FILE_NOT_FOUND'
        };
    }

    if (error instanceof ExcelError) {
        return {
            statusCode: error.statusCode,
            error: 'Excel Processing Error',
            message: error.message,
            code: error.code
        };
    }

    // Default error handling
    return {
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'An unexpected error occurred while processing the Excel file',
        code: 'INTERNAL_ERROR',
        originalError: process.env.NODE_ENV === 'development' ? error.message : undefined
    };
}

/**
 * Express error handling middleware
 * @param {Error} error - The error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function errorMiddleware(error, req, res, next) {
    const errorResponse = handleExcelError(error);
    
    // Log error details for debugging
    console.error('Error Details:', {
        url: req.url,
        method: req.method,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
    });

    res.status(errorResponse.statusCode).json(errorResponse);
}

/**
 * Async error wrapper for route handlers
 * @param {Function} fn - Async function to wrap
 * @returns {Function} - Wrapped function that catches errors
 */
function asyncErrorHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

/**
 * Validate and sanitize request data
 * @param {Object} data - Request data to validate
 * @param {Object} schema - Validation schema
 * @returns {Object} - Validated and sanitized data
 */
function validateRequest(data, schema) {
    const errors = [];
    const sanitized = {};

    for (const [field, rules] of Object.entries(schema)) {
        const value = data[field];

        if (rules.required && (value === undefined || value === null || value === '')) {
            errors.push(`${field} is required`);
            continue;
        }

        if (value !== undefined && value !== null) {
            if (rules.type && typeof value !== rules.type) {
                errors.push(`${field} must be of type ${rules.type}`);
                continue;
            }

            if (rules.validate && typeof rules.validate === 'function') {
                const validation = rules.validate(value);
                if (!validation.isValid) {
                    errors.push(`${field}: ${validation.message}`);
                    continue;
                }
                sanitized[field] = validation.value !== undefined ? validation.value : value;
            } else {
                sanitized[field] = value;
            }
        } else if (rules.default !== undefined) {
            sanitized[field] = rules.default;
        }
    }

    if (errors.length > 0) {
        throw new ValidationError(`Validation failed: ${errors.join(', ')}`);
    }

    return sanitized;
}

module.exports = {
    ExcelError,
    ValidationError,
    FileNotFoundError,
    handleExcelError,
    errorMiddleware,
    asyncErrorHandler,
    validateRequest
};
