const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const ExcelService = require('../services/excelService');
const WorksheetCloner = require('../services/worksheetCloner');
const { validateWorksheetName, validateNumberOfCopies, generateOutputFilename } = require('../utils/validation');
const { asyncErrorHandler, validateRequest, FileNotFoundError } = require('../utils/errorHandler');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
    dest: 'uploads/',
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.mimetype === 'application/vnd.ms-excel') {
            cb(null, true);
        } else {
            cb(new Error('Only Excel files are allowed!'), false);
        }
    },
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    }
});

/**
 * POST /api/excel/clone-worksheet
 * Clone worksheet from the existing Excel file in the workspace
 */
router.post('/clone-worksheet', asyncErrorHandler(async (req, res) => {
    // Validate request data
    const validatedData = validateRequest(req.body, {
        worksheetName: {
            type: 'string',
            validate: validateWorksheetName
        },
        numberOfCopies: {
            type: 'number',
            default: 3,
            validate: validateNumberOfCopies
        }
    });

    const { worksheetName, numberOfCopies } = validatedData;
        
        // Use the existing Excel file in the workspace
        const sourceFilePath = path.join(__dirname, '..', 'warehouse_explanation.xlsx');
        
        if (!fs.existsSync(sourceFilePath)) {
            throw new FileNotFoundError(
                'warehouse_explanation.xlsx not found in the workspace',
                sourceFilePath
            );
        }

        console.log(`Processing clone request for file: ${sourceFilePath}`);
        
        const excelService = new ExcelService();
        const worksheetCloner = new WorksheetCloner();

        // Read the Excel file
        const workbook = await excelService.readExcelFile(sourceFilePath);
        
        // Get worksheet information
        const worksheetInfo = excelService.getWorksheetInfo();
        console.log('Available worksheets:', worksheetInfo);

        // Determine which worksheet to clone
        let targetWorksheetName = worksheetName;
        if (!targetWorksheetName) {
            // If no worksheet name specified, use the first worksheet
            targetWorksheetName = worksheetInfo[0].name;
        }

        // Verify the worksheet exists
        const targetWorksheet = workbook.getWorksheet(targetWorksheetName);
        if (!targetWorksheet) {
            return res.status(400).json({
                error: 'Worksheet not found',
                message: `Worksheet '${targetWorksheetName}' not found`,
                availableWorksheets: worksheetInfo.map(ws => ws.name)
            });
        }

        // Get detailed information about the source worksheet
        const worksheetDetails = worksheetCloner.getWorksheetDetails(targetWorksheet);
        console.log('Source worksheet details:', worksheetDetails);

        // Clone the worksheet
        const clonedWorksheetNames = await worksheetCloner.cloneWorksheet(
            workbook, 
            targetWorksheetName, 
            numberOfCopies
        );

        // Generate output filename
        const outputFileName = generateOutputFilename('warehouse_explanation', 'cloned');
        const outputPath = path.join(__dirname, '..', 'output', outputFileName);

        // Save the workbook with cloned worksheets
        await excelService.saveWorkbook(outputPath);

        // Get final worksheet information
        const finalWorksheetInfo = excelService.getWorksheetInfo();

        res.json({
            success: true,
            message: `Successfully cloned worksheet '${targetWorksheetName}' ${numberOfCopies} times`,
            sourceWorksheet: {
                name: targetWorksheetName,
                details: worksheetDetails
            },
            clonedWorksheets: clonedWorksheetNames,
            outputFile: outputFileName,
            outputPath: outputPath,
            totalWorksheets: finalWorksheetInfo.length,
            worksheetInfo: finalWorksheetInfo
        });
}));

/**
 * POST /api/excel/upload-and-clone
 * Upload an Excel file and clone its worksheet
 */
router.post('/upload-and-clone', upload.single('excelFile'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                error: 'No file uploaded',
                message: 'Please upload an Excel file'
            });
        }

        const { worksheetName, numberOfCopies = 3 } = req.body;
        const uploadedFilePath = req.file.path;

        console.log(`Processing uploaded file: ${req.file.originalname}`);

        const excelService = new ExcelService();
        const worksheetCloner = new WorksheetCloner();

        // Read the uploaded Excel file
        const workbook = await excelService.readExcelFile(uploadedFilePath);
        
        // Get worksheet information
        const worksheetInfo = excelService.getWorksheetInfo();
        console.log('Available worksheets:', worksheetInfo);

        // Determine which worksheet to clone
        let targetWorksheetName = worksheetName;
        if (!targetWorksheetName) {
            targetWorksheetName = worksheetInfo[0].name;
        }

        // Verify the worksheet exists
        const targetWorksheet = workbook.getWorksheet(targetWorksheetName);
        if (!targetWorksheet) {
            // Clean up uploaded file
            fs.unlinkSync(uploadedFilePath);
            
            return res.status(400).json({
                error: 'Worksheet not found',
                message: `Worksheet '${targetWorksheetName}' not found`,
                availableWorksheets: worksheetInfo.map(ws => ws.name)
            });
        }

        // Get detailed information about the source worksheet
        const worksheetDetails = worksheetCloner.getWorksheetDetails(targetWorksheet);

        // Clone the worksheet
        const clonedWorksheetNames = await worksheetCloner.cloneWorksheet(
            workbook, 
            targetWorksheetName, 
            numberOfCopies
        );

        // Generate output filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const outputFileName = `cloned_${req.file.originalname.replace(/\.[^/.]+$/, '')}_${timestamp}.xlsx`;
        const outputPath = path.join(__dirname, '..', 'output', outputFileName);

        // Save the workbook with cloned worksheets
        await excelService.saveWorkbook(outputPath);

        // Clean up uploaded file
        fs.unlinkSync(uploadedFilePath);

        // Get final worksheet information
        const finalWorksheetInfo = excelService.getWorksheetInfo();

        res.json({
            success: true,
            message: `Successfully cloned worksheet '${targetWorksheetName}' ${numberOfCopies} times`,
            originalFile: req.file.originalname,
            sourceWorksheet: {
                name: targetWorksheetName,
                details: worksheetDetails
            },
            clonedWorksheets: clonedWorksheetNames,
            outputFile: outputFileName,
            outputPath: outputPath,
            totalWorksheets: finalWorksheetInfo.length,
            worksheetInfo: finalWorksheetInfo
        });

    } catch (error) {
        console.error('Error in upload-and-clone endpoint:', error);
        
        // Clean up uploaded file if it exists
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({
            error: 'Internal Server Error',
            message: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

/**
 * GET /api/excel/worksheet-info
 * Get information about worksheets in the existing Excel file
 */
router.get('/worksheet-info', asyncErrorHandler(async (_req, res) => {
    const sourceFilePath = path.join(__dirname, '..', 'warehouse_explanation.xlsx');

    if (!fs.existsSync(sourceFilePath)) {
        throw new FileNotFoundError(
            'warehouse_explanation.xlsx not found in the workspace',
            sourceFilePath
        );
    }

    const excelService = new ExcelService();
    await excelService.readExcelFile(sourceFilePath);

    const worksheetInfo = excelService.getWorksheetInfo();

    res.json({
        success: true,
        file: 'warehouse_explanation.xlsx',
        worksheetCount: worksheetInfo.length,
        worksheets: worksheetInfo
    });
}));

module.exports = router;
